# XYZ 停车数据库部署使用说明

## 项目概述

这是一个基于 Docker 的 MySQL 8.4 停车管理数据库系统，包含停车位、传感器、区域和标志牌等相关数据。

## 系统要求

- Docker 已安装并运行
- 端口 3306 可用
- 至少 1GB 可用磁盘空间

## 快速开始

### 第一步：启动数据库

```bash
sh ./start_db.sh
```

此命令将：
- 清理之前的容器和数据卷
- 构建 MySQL Docker 镜像
- 启动数据库容器

### 第二步：导入数据

等待 2-3 分钟让数据库完全启动后，执行：

```bash
chmod +x import_data.sh && ./import_data.sh
```

此命令将：
- 处理和清理 CSV 数据文件
- 将数据文件复制到容器内
- 执行数据库初始化脚本
- 导入所有停车相关数据

### 第三步：验证部署

连接到数据库验证安装：

```bash
docker exec -it xyz-parking-db-container mysql -uroot -proot
```

在 MySQL 命令行中执行：

```sql
USE xyz_parking_db;
SHOW TABLES;
SELECT COUNT(*) FROM PARKING_BAY;
SELECT COUNT(*) FROM PARKING_ZONE;
```

## 数据库配置

| 配置项 | 值 |
|--------|-----|
| 容器名称 | xyz-parking-db-container |
| 数据库名 | xyz_parking_db |
| 用户名 | root |
| 密码 | root |
| 端口 | 3306 |
| MySQL 版本 | 8.4 |

## 数据库结构

### 主要数据表

1. **PARKING_ZONE** - 停车区域表
   - `zone_id`: 区域ID（主键）

2. **PARKING_BAY_SENSOR** - 停车位传感器表
   - `kerbside_id`: 路边ID（主键）
   - `zone_id`: 区域ID
   - `status_sign`: 状态标志
   - `latitude`: 纬度
   - `longitude`: 经度
   - `status_timestamp`: 状态时间戳

3. **PARKING_BAY** - 停车位表
   - `kerbside_id`: 路边ID
   - `road_segment_id`: 路段ID
   - `road_segment_description`: 路段描述
   - `latitude`: 纬度
   - `longitude`: 经度

4. **PARKING_ZONE_SEG** - 停车区域段表
   - `zone_id`: 区域ID
   - `street_segment_id`: 街道段ID
   - `on_street`: 所在街道
   - `street_from`: 起始街道
   - `street_to`: 结束街道

5. **PARKING_SIGN_PLATE** - 停车标志牌表
   - `sign_id`: 标志ID（自增主键）
   - `zone_id`: 区域ID
   - `restriction_day`: 限制日期
   - `restriction_start_time`: 限制开始时间
   - `restriction_finish_time`: 限制结束时间
   - `restriction_display`: 限制显示
   - `duration_minutes`: 持续分钟数
   - `duration_hours`: 持续小时数
   - `max_stay_minutes`: 最大停留分钟数

## 数据文件说明

- `data1_clean.csv` - 清理后的数据文件1
- `data2_clean.csv` - 清理后的数据文件2（包含路边信息）
- `data3_clean.csv` - 清理后的数据文件3
- `data4_clean.csv` - 清理后的数据文件4（包含区域信息）

## 常用操作

### 查看所有表
```sql
USE xyz_parking_db;
SHOW TABLES;
```

### 查看表结构
```sql
DESCRIBE PARKING_BAY;
DESCRIBE PARKING_ZONE;
```

### 查询示例
```sql
-- 查看所有停车区域
SELECT * FROM PARKING_ZONE LIMIT 10;

-- 查看特定区域的停车位
SELECT * FROM PARKING_BAY_SENSOR WHERE zone_id = 'your_zone_id';

-- 查看停车标志牌限制
SELECT * FROM PARKING_SIGN_PLATE WHERE zone_id = 'your_zone_id';
```

## 故障排除

### 容器启动失败
```bash
# 检查 Docker 状态
docker ps -a

# 查看容器日志
docker logs xyz-parking-db-container

# 检查端口占用
lsof -i :3306
```

### 数据导入失败
```bash
# 检查数据文件是否存在
ls -la data/

# 重新运行导入脚本
./import_data.sh
```

### 连接数据库失败
```bash
# 确认容器正在运行
docker ps | grep xyz-parking-db-container

# 测试数据库连接
docker exec xyz-parking-db-container mysqladmin ping -uroot -proot
```

## 清理和维护

### 停止并删除容器
```bash
docker stop xyz-parking-db-container
docker rm xyz-parking-db-container
```

### 删除镜像
```bash
docker rmi parking-mysql8
```

### 清理数据卷
```bash
docker volume prune
```

## 备份和恢复

### 备份数据库
```bash
docker exec xyz-parking-db-container mysqldump -uroot -proot xyz_parking_db > backup.sql
```

### 恢复数据库
```bash
docker exec -i xyz-parking-db-container mysql -uroot -proot xyz_parking_db < backup.sql
```

## 注意事项

1. 确保 Docker 服务正在运行
2. 端口 3306 不被其他服务占用
3. 数据导入过程中请勿中断
4. 定期备份重要数据
5. 生产环境请修改默认密码

## 技术支持

如遇到问题，请检查：
1. Docker 版本兼容性
2. 系统资源是否充足
3. 网络连接是否正常
4. 文件权限是否正确

---

*最后更新：2025年8月*
