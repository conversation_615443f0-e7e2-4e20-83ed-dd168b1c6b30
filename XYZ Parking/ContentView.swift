//
//  ContentView.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI
import MapKit

// MARK: - Bottom Sheet Content Type
enum BottomSheetContentType {
    case list
    case detail
}

struct ContentView: View {
    @StateObject private var locationManager = LocationManager()
    @StateObject private var speechRecognizer = SpeechRecognizer()
    @State private var showingLocationAlert = false
    @State private var showingBottomSheet = true // Controls native sheet presentation
    @State private var searchText = ""
    @State private var selectedParkingFromMap: ParkingLocation? = nil // Track parking selected from map
    @State private var bottomSheetDetent: PresentationDetent = .height(90) // Control bottom sheet expansion
    @State private var centerOnParking: ParkingLocation? = nil // Track parking to center on map
    @State private var bottomSheetContentType: BottomSheetContentType = .list // Control what content to show
    @State private var selectedParkingLocation: ParkingLocation? = nil // Selected parking for detail view

    // Load parking data from JSON
    private let allParkingLocations: [ParkingLocation] = DataService.load("ParkingData.json")


    // Filtered parking locations based on search
    private var filteredParkingLocations: [ParkingLocation] {
        let filtered: [ParkingLocation]
        if searchText.isEmpty {
            filtered = allParkingLocations
        } else {
            filtered = allParkingLocations.filter { location in
                location.name.localizedCaseInsensitiveContains(searchText)
            }
        }

        // Sort by distance if user location is available
        if let userLocation = locationManager.location {
            return filtered.sorted { loc1, loc2 in
                let dist1 = CLLocation(latitude: loc1.latitude, longitude: loc1.longitude).distance(from: userLocation)
                let dist2 = CLLocation(latitude: loc2.latitude, longitude: loc2.longitude).distance(from: userLocation)
                return dist1 < dist2
            }
        } else {
            return Array(filtered.prefix(10)) // Show top 10 if no location
        }
    }

    var body: some View {
        NavigationStack {
            ZStack {
                // Main Map View with custom user location
                MapViewRepresentable(
                    locationManager: locationManager,
                    parkingLocations: allParkingLocations,
                    centerOnParking: $centerOnParking,
                    onParkingLocationSelected: { location in
                        // Set selected parking and switch to detail view
                        selectedParkingLocation = location
                        bottomSheetContentType = .detail
                        // Center map on selected parking location
                        centerOnParking = location
                        // Ensure bottom sheet is open and expanded
                        if !showingBottomSheet {
                            showingBottomSheet = true
                        }
                        // Expand bottom sheet to show details
                        bottomSheetDetent = .medium
                    }
                )
                .ignoresSafeArea()
                    .onAppear {
                        // LocationManager will automatically start location updates in init
                        // This is just a fallback in case user manually requests location
                        if locationManager.authorizationStatus == .authorizedWhenInUse ||
                           locationManager.authorizationStatus == .authorizedAlways {
                            locationManager.requestLocation()
                        }
                    }

                // Top Navigation Bar
                VStack {
                    HStack {
                        Text("XYZ Melbourne Parking")
                            .font(.title2)
                            .fontWeight(.bold)
                            .padding()
                            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12, style: .continuous))

                        Spacer()

                        // Location Button
                        Button(action: {
                            locationManager.recenterMapToUserLocation()
                        }) {
                            Image(systemName: "location.fill")
                                .font(.title2)
                                .foregroundStyle(.blue)
                        }
                        .padding(12)
                        .background(.regularMaterial)
                        .clipShape(Circle())
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 8)

                    Spacer()
                }

                // Bottom Sheet is now presented using .sheet() modifier
            }
        }
        .alert("Location Access Required", isPresented: $showingLocationAlert) {
            Button("Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Please enable location access in Settings to find parking near you.")
        }
        .onAppear(perform: speechRecognizer.requestPermission)
        .onChange(of: speechRecognizer.recognizedText) { _, newValue in
            searchText = newValue
        }
        .onChange(of: locationManager.authorizationStatus) { _, status in
            if status == .denied || status == .restricted {
                showingLocationAlert = true
            }
        }
        .sheet(isPresented: $showingBottomSheet) {
            ParkingBottomSheetView(
                searchText: $searchText,
                parkingLocations: filteredParkingLocations,
                userLocation: locationManager.location,
                speechRecognizer: speechRecognizer,
                contentType: $bottomSheetContentType,
                selectedParkingLocation: $selectedParkingLocation,
                onParkingLocationSelected: { location in
                    // Set selected parking and switch to detail view
                    selectedParkingLocation = location
                    bottomSheetContentType = .detail
                    bottomSheetDetent = .medium
                },
                onBackToList: {
                    // Return to list view
                    bottomSheetContentType = .list
                    selectedParkingLocation = nil
                    bottomSheetDetent = .medium
                }
            )
            .presentationDetents([.height(90), .medium, .large], selection: $bottomSheetDetent)
            .presentationDragIndicator(.visible)
            .presentationContentInteraction(.scrolls)
            .presentationBackgroundInteraction(.enabled)
            .presentationBackground(.ultraThinMaterial)
            .interactiveDismissDisabled(true)
        }
    }
}

#Preview {
    ContentView()
}

// MARK: - Parking Annotation
class ParkingAnnotation: NSObject, MKAnnotation {
    dynamic var coordinate: CLLocationCoordinate2D
    var title: String?
    var subtitle: String?
    let parkingLocation: ParkingLocation
    
    init(parkingLocation: ParkingLocation) {
        self.parkingLocation = parkingLocation
        self.coordinate = CLLocationCoordinate2D(latitude: parkingLocation.latitude, longitude: parkingLocation.longitude)
        self.title = parkingLocation.name
        self.subtitle = parkingLocation.address
        super.init()
    }
}

// MARK: - MapView UIViewRepresentable
struct MapViewRepresentable: UIViewRepresentable {
    @ObservedObject var locationManager: LocationManager
    let parkingLocations: [ParkingLocation]
    @Binding var centerOnParking: ParkingLocation?
    let onParkingLocationSelected: (ParkingLocation) -> Void

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator
        mapView.showsUserLocation = false // We'll handle this ourselves
        mapView.userTrackingMode = .none
        mapView.showsCompass = true
        mapView.showsScale = true

        // Register custom annotation view
        mapView.register(UserLocationAnnotationView.self,
                        forAnnotationViewWithReuseIdentifier: UserLocationAnnotationView.identifier)

        // Add parking annotations once during creation
        for location in parkingLocations {
            let annotation = ParkingAnnotation(parkingLocation: location)
            mapView.addAnnotation(annotation)
        }

        return mapView
    }

    func updateUIView(_ mapView: MKMapView, context: Context) {
        // Handle centering on selected parking location
        if let parkingToCenter = centerOnParking {
            let latitudeOffset = -0.003

            let region = MKCoordinateRegion(
                center: CLLocationCoordinate2D(
                    latitude: parkingToCenter.latitude + latitudeOffset,
                    longitude: parkingToCenter.longitude
                ),
                latitudinalMeters: 1000, // 1km zoom level
                longitudinalMeters: 1000
            )
            mapView.setRegion(region, animated: true)

            // Reset the centering trigger
            DispatchQueue.main.async {
                centerOnParking = nil
            }
            return // Skip other region updates when centering on parking
        }

        // Update map region only when explicitly requested (initial location or manual centering)
        if locationManager.shouldCenterOnRegionUpdate {
            let currentCenter = mapView.region.center
            let newCenter = locationManager.region.center
            let threshold = 0.001 // Only update if difference is significant

            if abs(currentCenter.latitude - newCenter.latitude) > threshold ||
               abs(currentCenter.longitude - newCenter.longitude) > threshold {
                mapView.setRegion(locationManager.region, animated: true)
            }

            // Reset the flag after updating
            DispatchQueue.main.async {
                locationManager.shouldCenterOnRegionUpdate = false
            }
        }



        // Update user location annotation (only user location, not parking annotations)
        if let userAnnotation = locationManager.userLocationAnnotation {
            if !mapView.annotations.contains(where: { $0 === userAnnotation }) {
                mapView.addAnnotation(userAnnotation)
            }

            // Update heading for existing annotation view
            if let annotationView = mapView.view(for: userAnnotation) as? UserLocationAnnotationView {
                annotationView.updateHeading(locationManager.userHeading)
            }
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, MKMapViewDelegate {
        var parent: MapViewRepresentable

        init(_ parent: MapViewRepresentable) {
            self.parent = parent
        }

        func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
            if annotation is UserLocationAnnotation {
                let annotationView = mapView.dequeueReusableAnnotationView(
                    withIdentifier: UserLocationAnnotationView.identifier,
                    for: annotation
                ) as! UserLocationAnnotationView

                // Update heading immediately
                annotationView.updateHeading(parent.locationManager.userHeading)

                return annotationView
            } else if annotation is ParkingAnnotation {
                // Handle parking annotations
                let identifier = "ParkingAnnotation"
                var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)

                if annotationView == nil {
                    annotationView = MKMarkerAnnotationView(annotation: annotation, reuseIdentifier: identifier)
                    annotationView?.canShowCallout = false
                } else {
                    annotationView?.annotation = annotation
                }

                // Customize the parking marker
                if let markerView = annotationView as? MKMarkerAnnotationView {
                    markerView.markerTintColor = .systemRed
                    markerView.glyphImage = UIImage(systemName: "car.fill")
                }

                return annotationView
            }
            return nil
        }

        func mapView(_ mapView: MKMapView, didAdd views: [MKAnnotationView]) {
            // Update heading for newly added user location annotation
            for view in views {
                if let userLocationView = view as? UserLocationAnnotationView {
                    userLocationView.updateHeading(parent.locationManager.userHeading)
                }
            }
        }
        
        func mapView(_ mapView: MKMapView, didSelect view: MKAnnotationView) {
            // Handle parking location selection
            if let parkingAnnotation = view.annotation as? ParkingAnnotation {
                parent.onParkingLocationSelected(parkingAnnotation.parkingLocation)
            }
        }
    }
}



// MARK: - Parking Bottom Sheet View
struct ParkingBottomSheetView: View {
    @Binding var searchText: String
    let parkingLocations: [ParkingLocation]
    let userLocation: CLLocation?
    @ObservedObject var speechRecognizer: SpeechRecognizer
    @Binding var contentType: BottomSheetContentType
    @Binding var selectedParkingLocation: ParkingLocation?
    let onParkingLocationSelected: (ParkingLocation) -> Void
    let onBackToList: () -> Void

    var body: some View {
        VStack(spacing: 0) {
            // Top bar with search or back button
            if contentType == .list {
                // Search bar (visible in list mode)
                HStack(spacing: 12) {
                    HStack(spacing: 8) {
                        Image(systemName: "magnifyingglass")
                            .foregroundStyle(.secondary)
                            .font(.system(size: 16))

                        TextField("Search", text: $searchText)
                            .textFieldStyle(.plain)
                            .font(.system(size: 16))

                        if !searchText.isEmpty {
                            Button(action: {
                                searchText = ""
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundStyle(.secondary)
                                    .font(.system(size: 16))
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(Color(.systemGray6))
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 25)
                            .stroke(Color(.systemGray4), lineWidth: 1)
                    )

                    // Voice search button
                    Button(action: {
                        if speechRecognizer.isRecording {
                            speechRecognizer.stopRecording()
                        } else {
                            speechRecognizer.startRecording()
                        }
                    }) {
                        Image(systemName: "mic.fill")
                            .font(.system(size: 20))
                            .foregroundStyle(.white)
                            .frame(width: 50, height: 50)
                            .background(Circle().fill(speechRecognizer.isRecording ? .red : .blue))
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            } else {
                // Back button bar (visible in detail mode)
                HStack {
                    Button(action: onBackToList) {
                        HStack(spacing: 8) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: 16, weight: .medium))
                            Text("Back to List")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .foregroundStyle(.blue)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }

            // Scrollable content
            ScrollViewReader { proxy in
                ScrollView {
                    VStack {
                        if contentType == .list {
                            // 显示列表视图内容，但不使用 NavigationStack
                            ParkingListContentView(
                                parkingLocations: parkingLocations,
                                searchText: searchText,
                                userLocation: userLocation,
                                onParkingLocationSelected: onParkingLocationSelected
                            )
                            .padding(.top, 4)
                        } else if let selectedLocation = selectedParkingLocation {
                            // 显示详情视图内容
                            ParkingDetailContentView(parkingLocation: selectedLocation)
                                .padding(.top, 4)
                        }
                    }
                    .id("top_scroll_view")
                }
                .onChange(of: contentType) {
                    withAnimation {
                        proxy.scrollTo("top_scroll_view", anchor: .top)
                    }
                }
                .onChange(of: selectedParkingLocation) {
                    if contentType == .detail {
                        withAnimation {
                            proxy.scrollTo("top_scroll_view", anchor: .top)
                        }
                    }
                }
            }
        }
        .background(
            // 使用半透明背景，保持地图可见性
            UnevenRoundedRectangle(
                topLeadingRadius: 16,
                bottomLeadingRadius: 0,
                bottomTrailingRadius: 0,
                topTrailingRadius: 16
            )
            .fill(.ultraThinMaterial)
            .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
            .ignoresSafeArea(.all, edges: .bottom) // 延伸到屏幕底部
        )
    }
}

// MARK: - Parking List Content View (without NavigationStack)
struct ParkingListContentView: View {
    let parkingLocations: [ParkingLocation]
    let searchText: String
    let userLocation: CLLocation?
    let onParkingLocationSelected: (ParkingLocation) -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // Section title
            HStack {
                Text("Parking Nearby")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundStyle(.primary)
                Spacer()
            }
            .padding(.horizontal, 20)

            // Parking list
            LazyVStack(spacing: 16) {
                if parkingLocations.isEmpty {
                    VStack(spacing: 12) {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 40))
                            .foregroundStyle(.secondary)
                        Text("No parking locations found")
                            .font(.headline)
                            .foregroundStyle(.secondary)
                        Text("Try adjusting your search terms")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    .padding(.top, 40)
                } else {
                    ForEach(Array(parkingLocations.enumerated()), id: \.element.id) { index, location in
                        ParkingLocationCard(
                            parkingLocation: location,
                            userLocation: userLocation,
                            isTopCard: index == 0,
                            onTap: {
                                onParkingLocationSelected(location)
                            }
                        )
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
    }
}

// MARK: - Parking Detail Content View (adapted for bottomsheet)
struct ParkingDetailContentView: View {
    let parkingLocation: ParkingLocation
    
    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            // Street view map
            StreetViewMapView(
                coordinate: CLLocationCoordinate2D(
                    latitude: parkingLocation.latitude,
                    longitude: parkingLocation.longitude
                ),
                parkingName: parkingLocation.name
            )
            .frame(height: 200)
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .padding(.horizontal, 20)
            
            // Details section
            VStack(alignment: .leading, spacing: 20) {
                // Title and address
                VStack(alignment: .leading, spacing: 8) {
                    Text(parkingLocation.name)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundStyle(.primary)

                    HStack(spacing: 6) {
                        Image(systemName: "location.fill")
                            .font(.system(size: 14))
                            .foregroundStyle(.blue)
                        Text(parkingLocation.address)
                            .font(.subheadline)
                            .foregroundStyle(.secondary)
                    }
                }

                // Price and availability info
                HStack(spacing: 24) {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Price")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                        HStack(spacing: 2) {
                            Text(parkingLocation.price)
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundStyle(.blue)
                            Text("/hr")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text("Available Spots")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                        HStack(spacing: 2) {
                            Text(parkingLocation.spots)
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundStyle(.green)
                            Text("spots")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                    }
                }

                // Action buttons
                Button(action: {
                    // Navigate action
                    if let url = URL(string: "maps://?daddr=\(parkingLocation.latitude),\(parkingLocation.longitude)") {
                        if UIApplication.shared.canOpenURL(url) {
                            UIApplication.shared.open(url)
                        }
                    }
                }) {
                    HStack {
                        Image(systemName: "location.north.fill")
                            .font(.system(size: 16, weight: .medium))
                        Text("Navigate")
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .foregroundStyle(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.blue)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.thinMaterial)
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            )
            .padding(.horizontal, 20)
        }
        .padding(.bottom, 20)
    }
}
