//
//  ParkingDetailView.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI
import MapKit

struct ParkingDetailView: View {
    let parkingLocation: ParkingLocation
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                // Street view map
                StreetViewMapView(
                    coordinate: CLLocationCoordinate2D(
                        latitude: parkingLocation.latitude,
                        longitude: parkingLocation.longitude
                    ),
                    parkingName: parkingLocation.name
                )
                .frame(height: 200)
                .clipShape(RoundedRectangle(cornerRadius: 16))
                .padding(.horizontal, 20)
                
                // Details section
                VStack(alignment: .leading, spacing: 20) {
                    // Title and address
                    VStack(alignment: .leading, spacing: 8) {
                        Text(parkingLocation.name)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundStyle(.primary)

                        HStack(spacing: 6) {
                            Image(systemName: "location.fill")
                                .font(.system(size: 14))
                                .foregroundStyle(.blue)
                            Text(parkingLocation.address)
                                .font(.subheadline)
                                .foregroundStyle(.secondary)
                        }
                    }

                    // Price and availability info
                    HStack(spacing: 24) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Price")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                            HStack(spacing: 2) {
                                Text(parkingLocation.price)
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .foregroundStyle(.blue)
                                Text("/hr")
                                    .font(.caption)
                                    .foregroundStyle(.secondary)
                            }
                        }

                        VStack(alignment: .leading, spacing: 4) {
                            Text("Available Spots")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                            HStack(spacing: 2) {
                                Text(parkingLocation.spots)
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .foregroundStyle(.green)
                                Text("spots")
                                    .font(.caption)
                                    .foregroundStyle(.secondary)
                            }
                        }
                    }

                    // Action buttons
                    Button(action: {
                        // Navigate action
                        if let url = URL(string: "maps://?daddr=\(parkingLocation.latitude),\(parkingLocation.longitude)") {
                            if UIApplication.shared.canOpenURL(url) {
                                UIApplication.shared.open(url)
                            }
                        }
                    }) {
                        HStack {
                            Image(systemName: "location.north.fill")
                                .font(.system(size: 16, weight: .medium))
                            Text("Navigate")
                                .font(.system(size: 16, weight: .semibold))
                        }
                        .foregroundStyle(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color.blue)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                    }
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(.thinMaterial)
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                )
                .padding(.horizontal, 20)
            }
            .padding(.bottom, 20)
        }
        .background(.ultraThinMaterial) 
        .navigationTitle("Parking Details")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Street View Map Component
struct StreetViewMapView: UIViewRepresentable {
    let coordinate: CLLocationCoordinate2D
    let parkingName: String

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator
        mapView.isScrollEnabled = false
        mapView.isZoomEnabled = false
        mapView.isRotateEnabled = false
        mapView.isPitchEnabled = false
        mapView.showsUserLocation = false

        // Set map type to satellite hybrid for better street view
        mapView.mapType = .satelliteFlyover

        // Create annotation for the parking location
        let annotation = MKPointAnnotation()
        annotation.coordinate = coordinate
        annotation.title = parkingName
        mapView.addAnnotation(annotation)

        // Set region to show the parking location
        let region = MKCoordinateRegion(
            center: coordinate,
            latitudinalMeters: 50,
            longitudinalMeters: 50
        )
        mapView.setRegion(region, animated: false)

        return mapView
    }

    func updateUIView(_ mapView: MKMapView, context: Context) {
        // Update the region if coordinate changes
        let region = MKCoordinateRegion(
            center: coordinate,
            latitudinalMeters: 50,
            longitudinalMeters: 50
        )
        mapView.setRegion(region, animated: true)
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, MKMapViewDelegate {
        var parent: StreetViewMapView

        init(_ parent: StreetViewMapView) {
            self.parent = parent
        }

        func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
            let identifier = "ParkingPin"
            var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)

            if annotationView == nil {
                annotationView = MKMarkerAnnotationView(annotation: annotation, reuseIdentifier: identifier)
                annotationView?.canShowCallout = false
            } else {
                annotationView?.annotation = annotation
            }

            // Customize the marker
            if let markerView = annotationView as? MKMarkerAnnotationView {
                markerView.markerTintColor = .systemRed
                markerView.glyphImage = UIImage(systemName: "car.fill")
            }

            return annotationView
        }
    }
}

// MARK: - Preview
#Preview {
    NavigationStack {
        ParkingDetailView(
            parkingLocation: ParkingLocation(
                id: 1,
                name: "Central Parking",
                address: "123 Collins Street, Melbourne VIC 3000",
                price: "$15",
                spots: "25",
                latitude: -37.8136,
                longitude: 144.9631
            )
        )
    }
}
