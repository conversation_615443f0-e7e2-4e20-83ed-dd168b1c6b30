//
//  ParkingLocationCard.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI
import CoreLocation

struct ParkingLocationCard: View {
    let parkingLocation: ParkingLocation
    let userLocation: CLLocation?
    let isTopCard: Bool
    let onTap: () -> Void

    var body: some View {
        VStack(spacing: 0) {
            // Top section with title and distance
            HStack(alignment: .top, spacing: 12) {
                // Parking icon
                Image(systemName: "car.fill")
                    .font(.title2)
                    .foregroundStyle(.blue)
                    .frame(width: 32, height: 32)
                    .background(
                        Circle()
                            .fill(.blue.opacity(0.1))
                    )

                // Main content
                VStack(alignment: .leading, spacing: 6) {
                    // Title and distance row
                    HStack(alignment: .firstTextBaseline) {
                        Text(parkingLocation.name)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundStyle(.primary)
                            .lineLimit(1)

                        Spacer()

                        // Distance badge
                        Text(parkingLocation.formattedDistance(from: userLocation))
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundStyle(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(.blue)
                            )
                    }

                    // Address
                    Text(parkingLocation.address)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                }

                // Arrow indicator
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundStyle(.secondary)
                    .padding(.top, 4)
            }
            .padding(.horizontal, 16)
            .padding(.top, 16)

            // Bottom section with price and spots
            HStack(spacing: 24) {
                // Price info
                HStack(spacing: 4) {
                    Image(systemName: "dollarsign.circle.fill")
                        .font(.caption)
                        .foregroundStyle(.blue)

                    Text(parkingLocation.price)
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundStyle(.blue)

                    Text("/hr")
                        .font(.caption2)
                        .foregroundStyle(.secondary)
                }

                Spacer()

                // Available spots
                HStack(spacing: 4) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.caption)
                        .foregroundStyle(.green)

                    Text(parkingLocation.spots)
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundStyle(.green)

                    Text("spots")
                        .font(.caption2)
                        .foregroundStyle(.secondary)
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
            .padding(.top, 8)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.thinMaterial)
                .shadow(color: .black.opacity(0.08), radius: 8, x: 0, y: 4)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(.quaternary, lineWidth: 0.5)
        )
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 16) {
        ParkingLocationCard(
            parkingLocation: ParkingLocation(
                id: 1,
                name: "Central Parking",
                address: "123 Collins Street, Melbourne VIC 3000",
                price: "$15",
                spots: "25",
                latitude: -37.8136,
                longitude: 144.9631
            ),
            userLocation: CLLocation(latitude: -37.8140, longitude: 144.9633),
            isTopCard: true,
            onTap: {}
        )

        ParkingLocationCard(
            parkingLocation: ParkingLocation(
                id: 2,
                name: "City Square Parking",
                address: "456 Bourke Street, Melbourne VIC 3000",
                price: "$12",
                spots: "18",
                latitude: -37.8140,
                longitude: 144.9633
            ),
            userLocation: nil,
            isTopCard: false,
            onTap: {}
        )
    }
    .padding()
    .background(Color(.systemGray6))
}
